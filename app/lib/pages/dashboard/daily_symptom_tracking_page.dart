import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:auto_route/auto_route.dart';
import 'package:intl/intl.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:account_management/application/symptom_tracking_bloc/symptom_tracking_bloc.dart';
import 'package:account_management/domain/model/symptom_model.dart';
import 'package:account_management/di/di.dart';
import 'package:design_system/design/theme.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';

import '../../custom_widgets/curved_app_bar.dart';
import '../../custom_widgets/weekly_calendar.dart';
import '../../custom_widgets/menstural_cycle_dial.dart';
import '../../services/cycle_day_calculator.dart';
import 'daily_symptom_page.dart';

@RoutePage()
class DailySymptomTrackingPage extends StatelessWidget {
  final DateTime? initialDate;

  const DailySymptomTrackingPage({Key? key, this.initialDate})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<PeriodTrackingWatcherBloc>(
          create: (context) => getIt<PeriodTrackingWatcherBloc>()
            ..add(PeriodTrackingWatcherEvent.watchYearStarted(
                (initialDate ?? DateTime.now()).year)),
        ),
        BlocProvider<SymptomTrackingBloc>(
          create: (context) => getIt<SymptomTrackingBloc>()
            ..add(SymptomTrackingEvent.loadSymptomData(
                date: initialDate ?? DateTime.now())),
        ),
      ],
      child: DailySymptomTrackingScaffold(initialDate: initialDate),
    );
  }
}

class DailySymptomTrackingScaffold extends StatefulWidget {
  final DateTime? initialDate;

  const DailySymptomTrackingScaffold({Key? key, this.initialDate})
      : super(key: key);

  @override
  State<DailySymptomTrackingScaffold> createState() =>
      _DailySymptomTrackingScaffoldState();
}

class _DailySymptomTrackingScaffoldState
    extends State<DailySymptomTrackingScaffold> {
  late DateTime _selectedDate;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate ?? DateTime.now();
  }

  void _onDateSelected(DateTime date) {
    setState(() {
      _selectedDate = date;
    });

    // Load symptom data for the new selected date
    context.read<SymptomTrackingBloc>().add(
          SymptomTrackingEvent.loadSymptomData(date: date),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Color(0xffFBF0D5),
      appBar: CurvedAppBar(
        appBarColor: AppTheme.primaryColor,
        logoColor: Color(0xffFAF2DF),
        subtitle: 'Daily Symptom Tracking',
        height: .35.sw,
        topLeftIcon: GestureDetector(
          onTap: () {
            context.router.pop();
          },
          child: Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              color: Color(0xffFAF2DF),
              shape: BoxShape.circle,
            ),
            child: Padding(
              padding: const EdgeInsets.all(6.0),
              child: Icon(
                Icons.arrow_back,
                color: Color(0xff30285D),
              ),
            ),
          ),
        ),
        topRightIcon: GestureDetector(
          onTap: () {
            context.router.push(PeriodTrackingViewRoute());
          },
          child: Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              color: Color(0xffFAF2DF),
              shape: BoxShape.circle,
            ),
            child: Padding(
              padding: const EdgeInsets.all(6.0),
              child: Icon(
                Icons.calendar_today_rounded,
                color: Color(0xff30285D),
              ),
            ),
          ),
        ),
      ),
      body: BlocListener<SymptomTrackingBloc, SymptomTrackingState>(
          listener: (context, state) {
            state.when(
              initial: (selectedDate) {
                if (selectedDate != null && selectedDate != _selectedDate) {
                  setState(() {
                    _selectedDate = selectedDate;
                  });
                }
              },
              loading: (selectedDate) {
                if (selectedDate != null && selectedDate != _selectedDate) {
                  setState(() {
                    _selectedDate = selectedDate;
                  });
                }
              },
              loaded: (selectedDate, _, __, ___) {
                if (selectedDate != _selectedDate) {
                  setState(() {
                    _selectedDate = selectedDate;
                  });
                }
              },
              success: (selectedDate) {
                if (selectedDate != _selectedDate) {
                  setState(() {
                    _selectedDate = selectedDate;
                  });
                }
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Symptoms saved successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              failure: (selectedDate, failure) {
                if (selectedDate != null && selectedDate != _selectedDate) {
                  setState(() {
                    _selectedDate = selectedDate;
                  });
                }
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to save symptoms'),
                    backgroundColor: Colors.red,
                  ),
                );
              },
            );
          },
          child: Stack(children: [
            // Gradient background
            Container(
              height: 1.sh,
              width: 1.sw,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xffFBF0D5), // Light cream
                    Color(0xffF8EEFF), // Light purple
                  ],
                ),
              ),
            ),

            SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                children: [
                  SizedBox(height: .38.sw),

                  // Weekly Calendar
                  BlocBuilder<PeriodTrackingWatcherBloc,
                      PeriodTrackingWatcherState>(
                    builder: (context, state) {
                      return state.when(
                        initial: () => Container(),
                        loading: () => Container(
                            height: 120.h,
                            child: Center(child: CircularProgressIndicator())),
                        loadSuccess: (yearData) {
                          // Extract period and ovulation dates
                          final periodDates = <DateTime>{};
                          final ovulationDates = <DateTime>{};

                          for (final monthData in yearData.values) {
                            for (final dayData in monthData.values) {
                              if (dayData.isPeriodDate == true &&
                                  dayData.date != null) {
                                periodDates.add(dayData.date!);
                              }
                              if (dayData.isOvulationDate == true &&
                                  dayData.date != null) {
                                ovulationDates.add(dayData.date!);
                              }
                            }
                          }

                          return WeeklyCalendar(
                            selectedDate: _selectedDate,
                            onDateSelected: _onDateSelected,
                            periodDates: periodDates,
                            ovulationDates: ovulationDates,
                          );
                        },
                        loadFailure: (failure) => Container(
                          height: 120.h,
                          child: Center(
                              child: Text('Failed to load calendar data')),
                        ),
                      );
                    },
                  ),

                  SizedBox(height: 24.h),

                  // Selected Date Info
                  Column(
                    children: [


                      SizedBox(

                        height: 25.h,
                      ),
                      // Menstrual Cycle Dial
                      BlocBuilder<PeriodTrackingWatcherBloc,
                          PeriodTrackingWatcherState>(
                        builder: (context, state) {
                          return state.when(
                            initial: () => Container(),
                            loading: () => Container(
                              height: 200.h,
                              child:
                                  Center(child: CircularProgressIndicator()),
                            ),
                            loadSuccess: (yearData) {
                              final cycleInfo =
                                  CycleDayCalculator.calculateCycleDay(
                                      _selectedDate, yearData);

                              if (cycleInfo == null) {
                                return Container(
                                  height: 200.h,
                                  child: Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(Icons.calendar_today,
                                            size: 48, color: Colors.grey),
                                        SizedBox(height: 8.h),
                                        Text(
                                          'No cycle data available',
                                          style: GoogleFonts.mulish(
                                            fontSize: 16.sp,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }

                              return Column(
                                children: [
                                  // Enhanced menstrual cycle tracker using calculated data
                                  MenstrualCycleTracker.fromCycleInfo(
                                    cycleInfo: cycleInfo,
                                    selectedDate: _selectedDate,
                                    size: 240,
                                    textStyle: GoogleFonts.mulish(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    outerCircleColor: Color(0xffD3CFC6),
                                    periodArcColor: AppTheme.primaryColor,
                                    ovulationArcColor: Color(0xffECA83D),
                                    innerCircleColor: AppTheme.primaryColor,
                                    pointerColor: AppTheme.primaryColor,
                                  ),
                                  SizedBox(height: 16.h),
                                ],
                              );
                            },
                            loadFailure: (failure) => Container(
                              height: 200.h,
                              child: Center(
                                  child: Text('Failed to load cycle data')),
                            ),
                          );
                        },
                      ),
                    ],
                  ),

                  SizedBox(height: 24.h),

                  // Symptoms Grid
                  BlocBuilder<SymptomTrackingBloc, SymptomTrackingState>(
                    builder: (context, state) {
                      return state.when(
                        initial: (selectedDate) => _buildEmptySymptomGrid(),
                        loading: (selectedDate) => Container(
                          margin: EdgeInsets.symmetric(horizontal: 24.w),
                          height: 200.h,
                          child: Center(child: CircularProgressIndicator()),
                        ),
                        loaded: (selectedDate, symptoms, painLevel,
                                flowLevel) =>
                            _buildSymptomGrid(symptoms, painLevel, flowLevel),
                        success: (selectedDate) => _buildEmptySymptomGrid(),
                        failure: (selectedDate, failure) =>
                            _buildEmptySymptomGrid(),
                      );
                    },
                  ),

                  SizedBox(height: 100.h), // Extra space for floating button
                ],
              ),
            ),
          ])),
      floatingActionButton: Container(
        margin: EdgeInsets.only(bottom: 20.h),
        child: FloatingActionButton.extended(
          
          onPressed: () {
            showModalBottomSheet<void>(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              builder: (context) => BlocProvider(
                create: (context) {
                  final bloc = getIt<SymptomTrackingBloc>();
                  // Initialize the bloc with the current selected date
                  bloc.add(SymptomTrackingEvent.loadSymptomData(
                      date: _selectedDate));
                  return bloc;
                },
                child: Container(
                  height: .9.sh,
                  child: DailySymptomPage(),
                ),
              ),
            );
          },
        
          backgroundColor: AppTheme.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(32),
          ),
          icon: Icon(Icons.add, color: Colors.white,size: 32,),
          label: Text(
            'Add Symptoms',
            style: GoogleFonts.mulish(
              fontSize: 25.sp,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildSymptomGrid(
      List<SymptomModel>? symptoms, int? painLevel, int? flowLevel) {
    List<Widget> chips = [];

    // Add pain chip if pain level > 0
    if (painLevel != null && painLevel > 0) {
      chips.add(_buildSymptomChip(
        name: 'Pain',
        iconPath: 'assets/home/<USER>', // Using cramps icon for pain
        value: painLevel.toString(),
        isSelected: true,
      ));
    }

    // Add flow chip if flow level > 0
    if (flowLevel != null && flowLevel > 0) {
      String flowText = '';
      switch (flowLevel) {
        case 1:
          flowText = 'Light';
          break;
        case 2:
          flowText = 'Medium';
          break;
        case 3:
          flowText = 'Heavy';
          break;
      }
      chips.add(_buildSymptomChip(
        name: 'Flow',
        iconPath: 'assets/home/<USER>',
        value: flowText,
        isSelected: true,
      ));
    }

    // Add symptom chips
    if (symptoms != null && symptoms.isNotEmpty) {
      for (final symptom in symptoms) {
        chips.add(_buildSymptomChip(
          name: symptom.name,
          iconPath:
              'assets/home/<USER>' ', '_')}.svg',
          isSelected: true,
        ));
      }
    }

    if (chips.isEmpty) {
      return _buildEmptySymptomGrid();
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 16.h),
          Wrap(
            spacing: 12.w,
            runSpacing: 12.h,
            children: chips,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptySymptomGrid() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
         
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(32.w),
            
            child: Column(
              children: [
                Icon(
                  Icons.add_circle_outline,
                  size: 48,
                  color: Colors.grey,
                ),
                SizedBox(height: 12.h),
                Text(
                  'No symptoms logged yet',
                  style: GoogleFonts.mulish(
                    fontSize: 25.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Tap "Add Symptoms" to get started',
                  style: GoogleFonts.mulish(
                    fontSize: 25.sp,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSymptomChip({
    required String name,
    required String iconPath,
    String? value,
    bool isSelected = false,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color:  Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child:
       Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 60.w,
            height: 60.w,
            decoration: BoxDecoration(
              color:  AppTheme.primaryColor,
              shape: BoxShape.circle,
            ),
            child: Padding(
              padding: EdgeInsets.all(8.w),
              child: SvgPicture.asset(
                iconPath,
                width: 16.w,
                height: 16.w,
                colorFilter: ColorFilter.mode(
                   Colors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            value != null ? '$name: $value' : name,
            style: GoogleFonts.mulish(
              fontSize: 25.sp,
              fontWeight: FontWeight.w600,
              color:  AppTheme.primaryColor,
            ),
          ),
          SizedBox(width: 8.w),
        ],
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }
}
